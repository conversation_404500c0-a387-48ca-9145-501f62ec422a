/**
 * 调试系统使用示例
 * 
 * 这个文件展示了如何使用新的 DebugLogger 调试系统
 */

// 1. 基本日志输出
function exampleBasicLogging() {
    // 调试信息（只在调试模式下显示）
    DebugLogger.debug('ExampleModule', '这是调试信息', { data: 'some debug data' })
    
    // 普通信息（总是显示）
    DebugLogger.info('ExampleModule', '这是普通信息', { status: 'ok' })
    
    // 警告信息
    DebugLogger.warn('ExampleModule', '这是警告信息', { warning: 'something might be wrong' })
    
    // 错误信息
    DebugLogger.error('ExampleModule', '这是错误信息', new Error('Something went wrong'))
    
    // 成功信息
    DebugLogger.success('ExampleModule', '操作成功完成', { result: 'success' })
}

// 2. 函数执行跟踪
function exampleFunctionTracing() {
    // 同步函数跟踪
    const result = DebugLogger.trace('ExampleModule', 'calculateSum', { a: 1, b: 2 }, () => {
        return 1 + 2
    })
    
    // 异步函数跟踪
    DebugLogger.traceAsync('ExampleModule', 'fetchData', { url: '/api/data' }, async () => {
        // 模拟异步操作
        await new Promise(resolve => setTimeout(resolve, 100))
        return { data: 'fetched data' }
    })
}

// 3. 日志历史管理
function exampleLogHistory() {
    // 获取所有日志历史
    const allLogs = DebugLogger.getHistory()
    console.log('所有日志:', allLogs)
    
    // 获取错误日志
    const errorLogs = DebugLogger.getHistory('ERROR', 50)
    console.log('错误日志:', errorLogs)
    
    // 导出日志历史
    const exportedLogs = DebugLogger.exportHistory()
    console.log('导出的日志:', exportedLogs)
    
    // 清空日志历史
    DebugLogger.clearHistory()
}

// 4. 在实际代码中的使用示例
function exampleRealWorldUsage() {
    try {
        DebugLogger.info('UserModule', '开始用户登录流程')
        
        // 模拟登录逻辑
        const loginData = { username: 'test', password: '***' }
        DebugLogger.debug('UserModule', '登录参数', loginData)
        
        // 模拟成功
        DebugLogger.success('UserModule', '用户登录成功', { userId: 123 })
        
    } catch (error) {
        DebugLogger.error('UserModule', '用户登录失败', error)
    }
}

// 5. 在浏览器控制台中的使用
/*
在浏览器控制台中，你可以这样使用：

// 查看当前调试模式状态
console.log('调试模式:', DebugLogger.isDebugMode)

// 手动开启调试模式
DebugLogger.init(true)

// 查看日志历史
DebugLogger.getHistory()

// 查看特定类型的日志
DebugLogger.getHistory('ERROR')

// 清空日志
DebugLogger.clearHistory()

// 导出日志用于分析
const logs = DebugLogger.exportHistory()
console.log(logs)
*/

export {
    exampleBasicLogging,
    exampleFunctionTracing,
    exampleLogHistory,
    exampleRealWorldUsage
}
