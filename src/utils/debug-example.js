/**
 * 调试系统使用示例
 * 
 * 这个文件展示了如何在项目中使用新的 DebugLogger 调试系统
 */

// 导入 ChannelChange 以获取 DebugLogger
import { ChannelChange } from '../views/ChannelChange.js'

/**
 * 示例：如何在函数中使用调试日志
 */
function exampleFunction() {
    // 调试信息 - 只在调试模式下显示
    DebugLogger.debug('ExampleModule', '开始执行示例函数')
    
    try {
        // 模拟一些业务逻辑
        const data = { userId: 123, action: 'click' }
        
        // 信息日志 - 总是显示
        DebugLogger.info('ExampleModule', '处理用户数据', data)
        
        // 模拟可能的警告
        if (!data.userId) {
            DebugLogger.warn('ExampleModule', '用户ID缺失', data)
        }
        
        // 成功日志
        DebugLogger.success('ExampleModule', '函数执行成功', { result: 'ok' })
        
    } catch (error) {
        // 错误日志
        DebugLogger.error('ExampleModule', '函数执行失败', error)
    }
}

/**
 * 示例：如何使用函数跟踪
 */
function exampleWithTrace() {
    return DebugLogger.trace('ExampleModule', 'exampleWithTrace', { param: 'test' }, () => {
        // 您的业务逻辑
        return 'success result'
    })
}

/**
 * 示例：如何使用异步函数跟踪
 */
async function exampleAsyncWithTrace() {
    return await DebugLogger.traceAsync('ExampleModule', 'exampleAsyncWithTrace', { param: 'test' }, async () => {
        // 模拟异步操作
        await new Promise(resolve => setTimeout(resolve, 100))
        return 'async success result'
    })
}

/**
 * 示例：如何查看和导出日志历史
 */
function debugLogManagement() {
    // 获取最近的错误日志
    const errorLogs = DebugLogger.getHistory('ERROR', 50)
    console.log('最近的错误日志:', errorLogs)
    
    // 获取所有日志
    const allLogs = DebugLogger.getHistory(null, 100)
    console.log('最近100条日志:', allLogs)
    
    // 导出日志为JSON
    const exportedLogs = DebugLogger.exportHistory()
    console.log('导出的日志JSON:', exportedLogs)
    
    // 清空日志历史
    // DebugLogger.clearHistory()
}

/**
 * 在 Vue 组件中的使用示例
 */
const vueComponentExample = {
    methods: {
        handleClick() {
            DebugLogger.debug('ButtonComponent', '按钮被点击')
            
            try {
                // 业务逻辑
                this.processClick()
                DebugLogger.success('ButtonComponent', '点击处理成功')
            } catch (error) {
                DebugLogger.error('ButtonComponent', '点击处理失败', error)
            }
        },
        
        async loadData() {
            return await DebugLogger.traceAsync('DataComponent', 'loadData', {}, async () => {
                const response = await fetch('/api/data')
                const data = await response.json()
                return data
            })
        }
    }
}

/**
 * 在 ChannelChange 类中的使用示例
 */
class ExampleChannelUsage {
    static initializePlatform(platformName) {
        DebugLogger.info('ChannelManager', '初始化平台', { platformName })
        
        // 检查平台是否支持
        const supportedPlatforms = ['facebook', 'tiktok', 'pinterest']
        if (!supportedPlatforms.includes(platformName)) {
            DebugLogger.warn('ChannelManager', '不支持的平台', { platformName, supportedPlatforms })
            return false
        }
        
        // 初始化逻辑
        DebugLogger.success('ChannelManager', '平台初始化成功', { platformName })
        return true
    }
}

export {
    exampleFunction,
    exampleWithTrace,
    exampleAsyncWithTrace,
    debugLogManagement,
    vueComponentExample,
    ExampleChannelUsage
}
