import { Dialog } from 'vant'
import lodash from 'lodash'

export const DEFAULT_URL_PARAMS_ENUM = {
    bookIdEnum: { // 书籍ID，例如：315
        key: 'book_id',
        // 举例：?book_id=315
        _description: '书籍ID',
    },
    bookVersionEnum: { // 书籍版本，例如：0
        key: 'book_version',
        // 举例：?book_version=0
        _description: '书籍版本',
    },
    bookVersionNameEnum: { // 书籍版本名称，例如：origin
        key: 'book_version_name',
        _description: '书籍版本名称',
    },
    channelEnum: { // 渠道标识，例如：facebook_ip_int
        key: 'channel',
        _description: '渠道标识',
    },
    chapterIdEnum: { // 章节ID，例如：2
        key: 'chapter_id',
        _description: '章节ID',
    },
    cpmEnum: { // CPM标识，例如：vdpPPNYkolNFLYOrMOeCCA
        key: 'cpm',
        _description: 'CPM标识',
    },
    packageNameEnum: { // 包名，例如：com.yestory.cc
        key: 'packageName',
        _description: '包名',
    },
    sourceEnum: { // 来源，例如：web2app_fb
        key: 'source',
        _description: '来源',
    },
    testSecretKeyEnum: { // 测试密钥，例如：kvBvawSSWrntdmZp
        key: 'testSecretKey',
        _description: '测试密钥',
    },
}
/**
 * 公共落地页 URL 宏
 */
export const PUBLIC_URL_PARAMS_ENUM = {
    campaignName: { // 广告系列名称
        key: 'utm_campaign',
        channelDefaultsValue: {
            facebookAndSnapchat: '{{campaign.name}}', 
            pinterest: '{campaign_name}',
        },
        _debugFillValue: '11',
    },
    campaignId: { // 广告系列ID
        key: 'utm_campaignid',
        channelDefaultsValue: {
            facebookAndSnapchat: '{{campaign.id}}', 
            pinterest: '{campaignid}',
        },
        _debugFillValue: '22',
    },
    adSetName: { // 广告组名称
        key: 'utm_adset',
        channelDefaultsValue: {
            facebookAndSnapchat: '{{adSet.name}}', 
            pinterest: '{ad_group_name}',
        },
        _debugFillValue: '33',
    },
    adSetId: { // 广告组ID 【注意⚠️：我们自己的H5，Facebook 渠道没有这个参数】
        key: 'utm_adsetid',
        channelDefaultsValue: {
            facebookAndSnapchat: '{{adSet.id}}', 
            pinterest: '{adgroupid}',
        },
        _debugFillValue: '44',
    },
    adVertisementId: { // 广告ID
        key: 'utm_adid',
        channelDefaultsValue: {
            facebookAndSnapchat: '{{ad.id}}', 
            pinterest: '{adid}',
        },
        _debugFillValue: '55',
    },
    adVertisementName: { // 广告名称
        key: 'utm_ad',
        channelDefaultsValue: {
            facebookAndSnapchat: '{{creative.name}}',
            pinterest: '{creative.name}',
        },
        _debugFillValue: '66',
    },
}
/**
 * Facebook 落地页 URL 宏说明
 */
export const FACEBOOK_URL_PARAMS_ENUM = {
    // ...lodash.omit(PUBLIC_URL_PARAMS_ENUM, ['adSetId']),
    // 
    pixelAccountId: { // 广告账户ID 
        key: 'pixelId',
        defaultValue: '',
        _debugFillValue: '87878',
    },
    // ---以下是正式环境才有的参数---
    channelClickId: { // 渠道点击id
        key: 'fbclid',
        defaultValue: '',
        _debugFillValue: '77',
    },
}

/**
 * Snapchat 落地页 URL 宏说明
 */
export const SNAPCHAT_URL_PARAMS_ENUM = {
    // 内部对接文档 https://alidocs.dingtalk.com/i/nodes/2Amq4vjg89RXzEvQCd2ArEd2W3kdP0wQ?rnd=0.****************&utm_medium=portal_recent&utm_source=portal&doc_type=wiki_doc
    /**
     * Snapchat 广告落地页 URL 宏
     */
    // ...PUBLIC_URL_PARAMS_ENUM,
    // 
    pixelAccountId: { // 广告账户ID 
        key: 'pixelId',
        defaultValue: '',
        _debugFillValue: '87878',
    },
    // ---以下是正式环境才有的参数---
    // ---以下是正式环境才有的参数---
    // ---以下是正式环境才有的参数---
    channelClickId: { // 渠道点击id https://developers.snap.com/api/marketing-api/Conversions-API/UsingTheAPI
        key: 'ScCid',
        defaultValue: '{{snap_user_id}}',
        _debugFillValue: '77',
    },
    snapchatUserId: { // 用户ID
        key: 'snap_user_id',
        defaultValue: '',
        _debugFillValue: '88',
    }
}
/**
 * Pinterest 落地页 URL 宏说明
 */
export const PINTEREST_URL_PARAMS_ENUM = {
    // 内部对接文档 https://alidocs.dingtalk.com/i/nodes/r1R7q3QmWe6yamDKC7ZDAqq1VxkXOEP2
    /**
     * Pinterest 广告落地页 URL 宏
     */
    // ...PUBLIC_URL_PARAMS_ENUM,
    //
    pixelAccountId: { // 广告账户ID
        key: 'pixelId',
        defaultValue: '',
        _debugFillValue: '*************',
    },
    // ---以下是正式环境才有的参数---
    channelClickId: { // 渠道点击id - 从 _epik cookie中提取
        key: 'epik',
        defaultValue: '',
        _debugFillValue: 'dj0yJnU9b2JDcFFHekV4SHJNcmVrbFBkUEdqakh0akdUT1VjVVUmcD0yJm49cnNBQ3F2Q2dOVDBXWWhkWklrUGxBUSZ0PUFBQUFBR1BaY3Bv',
    },
}
/**
 * 用于监听键盘按键事件的 Vue3 Hook（JS 版本，无类型注解）
 * @param {string|string[]} eventKey 需要监听的按键（单个或多个）
 * @param {function} callback 按下指定按键时执行的回调
 *
 * 例子：eventKey = 'Enter' 或 ['Enter', 'Escape']
 * 当用户按下 Enter 或 Escape 时，callback 会被调用
 */
export function useKeyupListener(eventKey, callback) {
    // 统一为数组，方便后续判断
    // 例如 eventKey = 'Enter'，则 keys = ['Enter']
    // 例如 eventKey = ['Enter', 'Escape']，则 keys = ['Enter', 'Escape']
    const keys = Array.isArray(eventKey) ? eventKey : [eventKey]

    // 事件处理函数
    // 例如 event.key = 'Enter'，keys = ['Enter', 'Escape']，则命中
    const handler = (event) => {
        if (keys.includes(event.key)) {
            // 命中监听的按键，执行回调
            // 例如：按下 'Enter'，callback(event) 被调用
            callback(event)
        }
    }

    // 立即添加事件监听
    window.addEventListener('keyup', handler)

    // 组件卸载时移除事件监听
    // window.removeEventListener('keyup', handler)

    // 返回清理函数，方便手动移除监听
    return () => {
        window.removeEventListener('keyup', handler)
    }
}
/**
 * 打印当前 URL 参数的工具函数
 * @param {string} [customTitle] - 自定义标题，默认为 "URL参数"
 * @returns {Record<string, string>} - URL 参数对象
 */
export async function logUrlParams(customTitle = "URL参数", curstomUrl = '') {
    console.log(`🚀 ====== 开始打印${customTitle} ======`)
    
    // 获取当前URL的完整路径
    const currentUrl = curstomUrl || window.location.href
    console.log(`📍 1. 原始路径:`, currentUrl)
    
    // 获取URL参数对象形式
    const urlParams = new URLSearchParams(currentUrl)
    const paramsObj = Object.fromEntries(urlParams.entries())
    console.log(`📋 2. search 对象形式:`, paramsObj)
    
    // 输出 window 的 origin、pathname、search
    // 判断是 curstomUrl 还是 window，分别打印信息
    if (curstomUrl) {
        // 解析 curstomUrl，举例说明
        // 例如 curstomUrl = "https://abc.com/path1/path2?x=1&y=2"
        const urlObj = new URL(curstomUrl)
        // urlObj.origin = "https://abc.com"
        // urlObj.pathname = "/path1/path2"
        // urlObj.search = "?x=1&y=2"
        console.log(`🌐 3. curstomUrl 信息:`, {
            origin: urlObj.origin,
            pathname: urlObj.pathname,
            search: urlObj.search
        })
    } else {
        // 没有传入 curstomUrl，打印 window 信息
        console.log(`🌐 3. window信息:`, {
            origin: window.location.origin,
            pathname: window.location.pathname,
            search: window.location.search
        })
    }
    
    console.log(`✅ ====== ${customTitle}打印完成 ======`)
    
    return paramsObj
  }
export const snpchatAutoFillParams = (params = {}, baseUrl, replaceResult) => {
      // 构造 URL 参数 [当前页面 url 参数]
    const searchParams = new URLSearchParams()
    // 如果 params 有传递值, 则替换 url 参数
    for (const [key, value] of Object.entries(params)) {
        searchParams.append(key, String(value))
    }

    let fillUrlParams = {}
    // 替换 url 参数,  从 SNAPCHAT_URL_PARAMS_ENUM 中获取 key 和 value
    for (const [key, value] of Object.entries(replaceResult)) {
        let fillUrlKey = value.key
        let fillUrlValue = value._debugFillValue
        if (fillUrlValue) {
            // 统一前缀，增加“规范一替换”相关 log，方便后续排查
            // console.log("[URL Snapchat URL替换][snpchatAutoFillParams] 替换前: key =", key, ", value =", value)
            searchParams.set(fillUrlKey, String(fillUrlValue))
            // 替换后查询是否修改成功
            // console.log("[URL Snapchat URL替换][snpchatAutoFillParams] 替换后: key =", fillUrlKey, ", value =", searchParams.get(fillUrlKey))
            fillUrlParams[fillUrlKey] = fillUrlValue
        }
        else {
            alert(`请在 SNAPCHAT_URL_PARAMS_ENUM 中配置 _debugFillValue,  否则无法自动填充参数,  key: ${key}, value: ${value}`)
        }
    }
    console.log("🚀 ~ snpchatAutoFillParams ~ fillUrlParams 替换过的参数:", fillUrlParams)
    
    // 拼接新 URL
    const url = baseUrl || `${window.location.origin}${window.location.pathname}`
    const newUrl = `${url}?${searchParams.toString()}`

    console.log("🚀 ~ snpchatAutoFillParams ~ newUrl:", newUrl)

    logUrlParams('snpchatAutoFillParams 手动填充参数', newUrl)

    // 弹窗提示跳转，并展示 newUrl
    // 这里用 vant 的 Dialog.confirm，用户点击确认后再跳转
    let message = `
        即将跳转到以下链接：\n${url}\n
        替换过的参数（fillUrlParams）：\n${JSON.stringify(fillUrlParams, null, 2)}
        具体参数如下：\n${JSON.stringify(Object.fromEntries(searchParams.entries()), null, 2)}
    `
    console.log("🚀 ~ snpchatAutoFillParams ~ message:", message)
    Dialog.confirm({
        title: '即将跳转',
        // 格式化展示 URL，分行显示参数，提升可读性
        // 增加展示替换过的参数 fillUrlParams，方便调试和核查
        message: message,
        confirmButtonText: '跳转',
        cancelButtonText: '取消'
    }).then(() => {
        // 用户点击“跳转”按钮
        let openInNewTab = 1
        if (openInNewTab) {
            // 在新标签页打开
            window.open(newUrl, '_blank')
        } else {
            // 当前页跳转
            window.location.href = newUrl
        }
    }).catch(() => {
        // 用户点击“取消”按钮，不做任何操作
        // 这里可以加日志或提示
        console.log('用户取消了跳转')
    })
}
export const channelRegisterAddEvent = () => {
    // 获取当前 url params 参数
    const urlParams = new URLSearchParams(window.location.search)
    const params = {
        // 增加参数，作用是不让点击底部按钮后跳转，用于调试
        'stop': true,
    }
    for (const [key, value] of urlParams.entries()) {
        params[key] = value
    }
    /**
     * 买量参数填充
     * 按下 p 键，自动填充 Snapchat 参数
     * 按下 f 键，自动填充 Facebook 参数
     * 按下 t 键，自动填充 Pinterest 参数
     */
    useKeyupListener('p', () => {
        snpchatAutoFillParams(params, null, {
            ...SNAPCHAT_URL_PARAMS_ENUM,
            ...PUBLIC_URL_PARAMS_ENUM,
        })
    })
    useKeyupListener('t', () => {
        snpchatAutoFillParams(params, null, {
            ...PINTEREST_URL_PARAMS_ENUM,
            ...PUBLIC_URL_PARAMS_ENUM,
        })
    })
    useKeyupListener('f', () => {
        snpchatAutoFillParams(params, null, {
            ...FACEBOOK_URL_PARAMS_ENUM,
            ...lodash.omit(PUBLIC_URL_PARAMS_ENUM, ['adSetId']),
        })
    })
}
